import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/models/post_model.dart';
import 'package:gameflex_mobile/models/like_model.dart';

void main() {
  group('Like System Tests', () {
    test('PostModel should handle like status correctly', () {
      // Test creating a post with like status
      final post = PostModel(
        id: 'test-post-1',
        userId: 'user-1',
        content: 'Test post content',
        likeCount: 5,
        commentCount: 2,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isLikedByCurrentUser: true,
      );

      expect(post.isLikedByCurrentUser, true);
      expect(post.likeCount, 5);
      expect(post.id, 'test-post-1');
    });

    test('PostModel copyWith should update like status', () {
      final originalPost = PostModel(
        id: 'test-post-1',
        userId: 'user-1',
        content: 'Test post content',
        likeCount: 5,
        commentCount: 2,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isLikedByCurrentUser: false,
      );

      // Test liking the post
      final likedPost = originalPost.copyWith(
        likeCount: 6,
        isLikedByCurrentUser: true,
      );

      expect(likedPost.isLikedByCurrentUser, true);
      expect(likedPost.likeCount, 6);
      expect(likedPost.id, originalPost.id); // Should remain the same
      expect(likedPost.content, originalPost.content); // Should remain the same

      // Test unliking the post
      final unlikedPost = likedPost.copyWith(
        likeCount: 5,
        isLikedByCurrentUser: false,
      );

      expect(unlikedPost.isLikedByCurrentUser, false);
      expect(unlikedPost.likeCount, 5);
    });

    test('PostModel fromJson should parse like status', () {
      final json = {
        'id': 'test-post-1',
        'user_id': 'user-1',
        'content': 'Test post content',
        'like_count': 10,
        'comment_count': 3,
        'is_active': true,
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-01T00:00:00.000Z',
        'is_liked_by_current_user': true,
        'username': 'testuser',
        'display_name': 'Test User',
      };

      final post = PostModel.fromJson(json);

      expect(post.isLikedByCurrentUser, true);
      expect(post.likeCount, 10);
      expect(post.username, 'testuser');
    });

    test('PostModel toJson should include like status', () {
      final post = PostModel(
        id: 'test-post-1',
        userId: 'user-1',
        content: 'Test post content',
        likeCount: 7,
        commentCount: 1,
        isActive: true,
        createdAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
        isLikedByCurrentUser: true,
        username: 'testuser',
      );

      final json = post.toJson();

      expect(json['is_liked_by_current_user'], true);
      expect(json['like_count'], 7);
      expect(json['id'], 'test-post-1');
    });

    test('LikeModel should be created correctly', () {
      final like = LikeModel(
        id: 'like-1',
        userId: 'user-1',
        postId: 'post-1',
        createdAt: DateTime.now(),
        username: 'testuser',
        displayName: 'Test User',
        postContent: 'Liked post content',
      );

      expect(like.isPostLike, true);
      expect(like.isCommentLike, false);
      expect(like.username, 'testuser');
      expect(like.postContent, 'Liked post content');
    });

    test('LikeModel fromJson should parse correctly', () {
      final json = {
        'id': 'like-1',
        'user_id': 'user-1',
        'post_id': 'post-1',
        'created_at': '2024-01-01T00:00:00.000Z',
        'username': 'testuser',
        'display_name': 'Test User',
        'post_content': 'Liked post content',
      };

      final like = LikeModel.fromJson(json);

      expect(like.id, 'like-1');
      expect(like.userId, 'user-1');
      expect(like.postId, 'post-1');
      expect(like.isPostLike, true);
      expect(like.username, 'testuser');
    });

    test('LikeModel timeAgo should work correctly', () {
      final now = DateTime.now();
      
      // Test "Just now"
      final justNow = LikeModel(
        id: 'like-1',
        userId: 'user-1',
        postId: 'post-1',
        createdAt: now,
      );
      expect(justNow.timeAgo, 'Just now');

      // Test minutes ago
      final minutesAgo = LikeModel(
        id: 'like-2',
        userId: 'user-1',
        postId: 'post-1',
        createdAt: now.subtract(const Duration(minutes: 5)),
      );
      expect(minutesAgo.timeAgo, '5m ago');

      // Test hours ago
      final hoursAgo = LikeModel(
        id: 'like-3',
        userId: 'user-1',
        postId: 'post-1',
        createdAt: now.subtract(const Duration(hours: 2)),
      );
      expect(hoursAgo.timeAgo, '2h ago');

      // Test days ago
      final daysAgo = LikeModel(
        id: 'like-4',
        userId: 'user-1',
        postId: 'post-1',
        createdAt: now.subtract(const Duration(days: 3)),
      );
      expect(daysAgo.timeAgo, '3d ago');
    });

    test('PostModel equality should work correctly', () {
      final post1 = PostModel(
        id: 'test-post-1',
        userId: 'user-1',
        content: 'Test content',
        likeCount: 5,
        commentCount: 2,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final post2 = PostModel(
        id: 'test-post-1',
        userId: 'user-2', // Different user
        content: 'Different content',
        likeCount: 10,
        commentCount: 5,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final post3 = PostModel(
        id: 'test-post-2', // Different ID
        userId: 'user-1',
        content: 'Test content',
        likeCount: 5,
        commentCount: 2,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      expect(post1, equals(post2)); // Same ID, should be equal
      expect(post1, isNot(equals(post3))); // Different ID, should not be equal
      expect(post1.hashCode, equals(post2.hashCode)); // Same ID, same hash
    });
  });
}
