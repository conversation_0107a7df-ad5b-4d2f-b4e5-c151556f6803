import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:flutter/foundation.dart';

void main() {
  group('iOS Environment Configuration Tests', () {
    test('iOS should use localhost for development', () {
      // This test runs in development mode by default (STAGING=false)
      final url = SupabaseService.supabaseUrl;

      // On iOS, should use localhost for development
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        expect(
          url.contains('localhost'),
          true,
          reason: 'iOS development should use localhost URLs, got: $url',
        );

        // Should not use staging URL
        expect(
          url.contains('gameflex.io'),
          false,
          reason: 'iOS development should not use staging URLs, got: $url',
        );
      }
    });

    test('iOS URL transformation should work based on environment', () {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      // Test URL transformations for iOS
      final testUrls = [
        'http://localhost:8090/storage/v1/object/public/uploads/test.jpg',
        'http://gameflex.local:8090/storage/v1/object/public/uploads/test.jpg',
        'http://api.gameflex.local:8090/rest/v1/posts',
      ];

      for (final url in testUrls) {
        final transformed = SupabaseService.transformUrl(url);

        if (isStaging) {
          // In staging, URLs should be transformed to dev.api.gameflex.io
          expect(
            transformed?.contains('dev.api.gameflex.io'),
            true,
            reason:
                'iOS URL should use staging addresses in staging, got: $transformed',
          );
        } else {
          // In development on iOS, URLs should remain as localhost or be transformed to ********
          expect(
            (transformed?.contains('localhost') ?? false) ||
                (transformed?.contains('********') ?? false),
            true,
            reason:
                'iOS URL should use local addresses in development, got: $transformed',
          );

          // Should not contain staging URLs in development
          expect(
            transformed?.contains('gameflex.io'),
            false,
            reason:
                'Transformed URL should not contain staging domain in development, got: $transformed',
          );
        }
      }
    });

    test('Environment detection should work correctly', () {
      const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

      // Verify the service reflects the current environment
      final url = SupabaseService.supabaseUrl;

      if (isStaging) {
        // In staging mode, should use staging URLs
        expect(
          url.contains('dev.api.gameflex.io'),
          true,
          reason: 'Staging mode should use staging URLs, got: $url',
        );
      } else {
        // In development mode, should use local URLs
        expect(
          url.contains('gameflex.io'),
          false,
          reason: 'Development mode should not use staging URLs, got: $url',
        );
      }
    });

    test('Anonymous key should be available for iOS', () {
      final anonKey = SupabaseService.supabaseAnonKey;

      expect(
        anonKey.isNotEmpty,
        true,
        reason: 'Anonymous key should not be empty',
      );
      expect(
        anonKey.startsWith('eyJ'),
        true,
        reason: 'Anonymous key should be a valid JWT token',
      );
    });
  });
}
