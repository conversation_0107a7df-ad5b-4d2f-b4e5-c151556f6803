import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';

void main() {
  group('Backend Media API Tests', () {
    const baseUrl = 'http://localhost:8090';
    const apiKey =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';

    late String accessToken;
    late String userId;

    setUpAll(() async {
      // Authenticate to get access token using the correct endpoint
      final authResponse = await http.post(
        Uri.parse('$baseUrl/auth/v1/token?grant_type=password'),
        headers: {'Content-Type': 'application/json', 'apikey': apiKey},
        body: jsonEncode({
          'email': '<EMAIL>',
          'password': 'devpassword123',
        }),
      );

      print('Auth response status: ${authResponse.statusCode}');
      print('Auth response body: ${authResponse.body}');

      // If password auth fails, let's just use a known user ID for testing
      if (authResponse.statusCode != 200) {
        print('⚠️ Password auth failed, using direct user ID for testing');
        userId = '00000000-0000-0000-0000-000000000001'; // dev user
        accessToken = apiKey; // Use API key as token for testing
      } else {
        final authData = jsonDecode(authResponse.body);
        accessToken = authData['access_token'];
        userId = authData['user']['id'];
        print('✅ Authenticated as: ${authData['user']['email']}');
      }
    });

    test('Test media table and post creation with new structure', () async {
      print('🧪 Testing new media table structure...');

      try {
        // 1. Create a media record
        final mediaResponse = await http.post(
          Uri.parse('$baseUrl/rest/v1/media'),
          headers: {
            'Content-Type': 'application/json',
            'apikey': apiKey,
            'Authorization': 'Bearer $accessToken',
            'Prefer': 'return=representation',
          },
          body: jsonEncode({
            'type': 'image',
            'location': 'user',
            'name': 'test_image_${DateTime.now().millisecondsSinceEpoch}',
            'extension': 'jpg',
            'channel_id': null,
            'owner_id': userId,
            'bucket_location': 'storage/v1/object',
            'bucket_name': 'media',
            'bucket_permission': 'public',
          }),
        );

        expect(mediaResponse.statusCode, 201);
        final mediaData = jsonDecode(mediaResponse.body)[0];
        final mediaId = mediaData['id'];
        print('✅ Media record created: $mediaId');

        // 2. Create a post that references the media
        final postResponse = await http.post(
          Uri.parse('$baseUrl/rest/v1/posts'),
          headers: {
            'Content-Type': 'application/json',
            'apikey': apiKey,
            'Authorization': 'Bearer $accessToken',
            'Prefer': 'return=representation',
          },
          body: jsonEncode({
            'user_id': userId,
            'media_id': mediaId,
            'content': 'Test post with new media structure',
            'channel_id': null,
          }),
        );

        expect(postResponse.statusCode, 201);
        final postData = jsonDecode(postResponse.body)[0];
        print('✅ Post created: ${postData['id']}');

        // 3. Verify the relationship by fetching posts with media
        final fetchResponse = await http.get(
          Uri.parse(
            '$baseUrl/rest/v1/posts?select=*,media(*)&id=eq.${postData['id']}',
          ),
          headers: {'apikey': apiKey, 'Authorization': 'Bearer $accessToken'},
        );

        expect(fetchResponse.statusCode, 200);
        final fetchedPosts = jsonDecode(fetchResponse.body);
        expect(fetchedPosts, isNotEmpty);

        final post = fetchedPosts[0];
        expect(post['media_id'], equals(mediaId));
        expect(post['media'], isNotNull);
        expect(post['media']['id'], equals(mediaId));
        expect(post['media']['type'], equals('image'));
        expect(post['media']['location'], equals('user'));

        print('✅ Post-media relationship verified');

        // 4. Test URL construction logic
        final media = post['media'];
        final constructedUrl = _constructMediaUrl(media);
        print('🔗 Constructed URL: $constructedUrl');

        expect(constructedUrl, contains('http://localhost:8090'));
        expect(constructedUrl, contains('storage/v1/object/public/media'));
        expect(constructedUrl, contains('user'));
        expect(constructedUrl, contains(userId));
        expect(constructedUrl, contains('.jpg'));

        print('🎉 All backend media tests passed!');
      } catch (e) {
        print('❌ Test failed: $e');
        rethrow;
      }
    });

    test('Test media URL construction for different scenarios', () async {
      print('🧪 Testing media URL construction...');

      // Test user media (no channel)
      final userMedia = {
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final userUrl = _constructMediaUrl(userMedia);
      expect(
        userUrl,
        equals(
          'http://localhost:8090/storage/v1/object/public/media/user/user123/test_image.jpg',
        ),
      );
      print('✅ User media URL: $userUrl');

      // Test channel media
      final channelMedia = {
        'location': 'minecraft',
        'name': 'castle_build',
        'extension': 'png',
        'channel_id': 'channel456',
        'owner_id': 'user123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final channelUrl = _constructMediaUrl(channelMedia);
      expect(
        channelUrl,
        equals(
          'http://localhost:8090/storage/v1/object/public/media/minecraft/user123/channel456/castle_build.png',
        ),
      );
      print('✅ Channel media URL: $channelUrl');

      print('🎉 URL construction tests passed!');
    });
  });
}

/// Construct full URL from media record (matches Flutter helper)
String _constructMediaUrl(Map<String, dynamic> media, {String? baseUrl}) {
  final location = media['location'] as String;
  final name = media['name'] as String;
  final extension = media['extension'] as String;
  final channelId = media['channel_id'] as String?;
  final ownerId = media['owner_id'] as String;
  final bucketLocation =
      media['bucket_location'] as String? ?? 'storage/v1/object';
  final bucketName = media['bucket_name'] as String? ?? 'media';
  final bucketPermission = media['bucket_permission'] as String? ?? 'public';

  // Use provided baseUrl or default to environment-aware URL
  final host = baseUrl ?? 'http://localhost:8090'; // Test default

  // Construct path based on location and ownership
  String path;
  if (channelId != null) {
    path = '$location/$ownerId/$channelId/$name.$extension';
  } else {
    path = '$location/$ownerId/$name.$extension';
  }

  return '$host/$bucketLocation/$bucketPermission/$bucketName/$path';
}
