import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() {
  group('Simple Post Creation Tests', () {
    late SupabaseClient client;

    setUpAll(() async {
      // Initialize Supabase directly without shared preferences
      client = SupabaseClient(
        'http://localhost:8000',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
      );

      // Authenticate as dev user
      try {
        await client.auth.signInWithPassword(
          email: '<EMAIL>',
          password: 'devpassword123',
        );
        print('✅ <NAME_EMAIL>');
      } catch (e) {
        print('❌ Authentication failed: $e');
      }
    });

    test('Test Database Connection', () async {
      try {
        final response = await client.from('posts').select('count').count();
        print('✅ Database connection successful. Post count: $response');
        expect(response, isA<int>());
      } catch (e) {
        print('❌ Database connection failed: $e');
        fail('Database connection failed: $e');
      }
    });

    test('Test Storage Bucket Access', () async {
      try {
        final buckets = await client.storage.listBuckets();
        print('✅ Storage connection successful');
        print('Available buckets: ${buckets.map((b) => b.name).join(', ')}');

        final mediaBucket = buckets.where((b) => b.name == 'media').firstOrNull;
        expect(mediaBucket, isNotNull, reason: 'Media bucket should exist');
        print('✅ Media bucket found: ${mediaBucket!.name}');
      } catch (e) {
        print('❌ Storage connection failed: $e');
        fail('Storage connection failed: $e');
      }
    });

    test('Test Post Creation', () async {
      try {
        final testPost = {
          'user_id':
              '600b483e-12fa-4e8d-9e65-e81f0324d0c9', // Authenticated dev user ID
          'content': 'Test post created by Flutter test',
          'media_type': 'image',
          'media_url':
              'http://localhost:8000/storage/v1/object/public/media/test.jpg',
          'channel_id': null,
          'like_count': 0,
          'comment_count': 0,
          'is_active': true,
        };

        final response = await client.from('posts').insert(testPost).select();

        if (response.isNotEmpty) {
          print('✅ Post creation successful');
          print('Created post ID: ${response.first['id']}');

          // Clean up - delete the test post
          await client.from('posts').delete().eq('id', response.first['id']);
          print('✅ Test post cleaned up');

          expect(response.first['id'], isNotNull);
        } else {
          print('❌ Post creation returned empty response');
          fail('Post creation failed');
        }
      } catch (e) {
        print('❌ Post creation failed: $e');
        fail('Post creation failed: $e');
      }
    });
  });
}
