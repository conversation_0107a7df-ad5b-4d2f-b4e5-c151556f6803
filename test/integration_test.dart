import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gameflex_mobile/main.dart' as app;
import 'package:gameflex_mobile/services/supabase_service.dart';
import 'package:gameflex_mobile/services/upload_service.dart';
import 'package:gameflex_mobile/services/auth_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('GameFlex Integration Tests', () {
    setUpAll(() async {
      // Initialize the app
      await SupabaseService.initialize();
    });

    testWidgets('Test complete upload flow with new media structure', (WidgetTester tester) async {
      print('🧪 Starting integration test for media upload...');

      try {
        // Start the app
        app.main();
        await tester.pumpAndSettle();

        // Test authentication first
        final authService = AuthService();
        print('🔐 Testing authentication...');
        
        final authResult = await authService.signIn(
          email: '<EMAIL>',
          password: 'devpassword123',
        );

        expect(authResult.success, isTrue, reason: 'Authentication should succeed');
        print('✅ Authentication successful');

        // Create a test image file
        final testImageBytes = _createBlackJPEG();
        final tempDir = Directory.systemTemp;
        final testImageFile = File('${tempDir.path}/integration_test_image.jpg');
        await testImageFile.writeAsBytes(testImageBytes);
        print('📸 Created test image: ${testImageFile.path}');

        // Test the upload service with new media structure
        final uploadService = UploadService();
        print('📤 Testing upload with new media structure...');
        
        final uploadResult = await uploadService.createPost(
          imageFile: testImageFile,
          content: 'Integration test post with new media structure - ${DateTime.now().millisecondsSinceEpoch}',
        );

        expect(uploadResult, isTrue, reason: 'Upload should succeed');
        print('✅ Upload successful with new media structure');

        // Verify the post was created with proper media relationship
        final supabase = SupabaseService.client;
        final posts = await supabase
            .from('posts')
            .select('*, media(*)')
            .eq('content', 'Integration test post with new media structure - ${DateTime.now().millisecondsSinceEpoch}')
            .limit(1);

        expect(posts, isNotEmpty, reason: 'Post should be found in database');
        expect(posts.first['media_id'], isNotNull, reason: 'Post should have media_id');
        expect(posts.first['media'], isNotNull, reason: 'Post should have media relationship');
        
        final media = posts.first['media'];
        expect(media['type'], equals('image'));
        expect(media['location'], equals('user'));
        expect(media['extension'], equals('jpg'));
        expect(media['owner_id'], isNotNull);
        
        print('✅ Post-media relationship verified');
        print('📊 Media record: ${media['id']}');
        print('📊 Post record: ${posts.first['id']}');

        // Test URL construction
        final constructedUrl = _constructMediaUrl(media);
        expect(constructedUrl, contains('http://localhost:8090'));
        expect(constructedUrl, contains('storage/v1/object/public/media'));
        print('🔗 Constructed URL: $constructedUrl');

        // Clean up test file
        if (await testImageFile.exists()) {
          await testImageFile.delete();
        }

        print('🎉 Integration test completed successfully!');
      } catch (e, stackTrace) {
        print('❌ Integration test failed: $e');
        print('Stack trace: $stackTrace');
        rethrow;
      }
    });

    testWidgets('Test media URL construction for different scenarios', (WidgetTester tester) async {
      print('🧪 Testing URL construction scenarios...');

      // Test user media (no channel)
      final userMedia = {
        'location': 'user',
        'name': 'test_image',
        'extension': 'jpg',
        'channel_id': null,
        'owner_id': 'user123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final userUrl = _constructMediaUrl(userMedia);
      expect(userUrl, equals('http://localhost:8090/storage/v1/object/public/media/user/user123/test_image.jpg'));
      print('✅ User media URL: $userUrl');

      // Test channel media
      final channelMedia = {
        'location': 'minecraft',
        'name': 'castle_build',
        'extension': 'png',
        'channel_id': 'channel456',
        'owner_id': 'user123',
        'bucket_location': 'storage/v1/object',
        'bucket_name': 'media',
        'bucket_permission': 'public',
      };

      final channelUrl = _constructMediaUrl(channelMedia);
      expect(channelUrl, equals('http://localhost:8090/storage/v1/object/public/media/minecraft/user123/channel456/castle_build.png'));
      print('✅ Channel media URL: $channelUrl');

      print('🎉 URL construction tests passed!');
    });
  });
}

/// Create a minimal black JPEG image (9:16 aspect ratio)
Uint8List _createBlackJPEG() {
  return Uint8List.fromList([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x01, 0x40,
    0x00, 0xB4, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF,
    0xD9
  ]);
}

/// Construct full URL from media record
String _constructMediaUrl(Map<String, dynamic> media, {String? baseUrl}) {
  final location = media['location'] as String;
  final name = media['name'] as String;
  final extension = media['extension'] as String;
  final channelId = media['channel_id'] as String?;
  final ownerId = media['owner_id'] as String;
  final bucketLocation = media['bucket_location'] as String? ?? 'storage/v1/object';
  final bucketName = media['bucket_name'] as String? ?? 'media';
  final bucketPermission = media['bucket_permission'] as String? ?? 'public';
  
  final host = baseUrl ?? 'http://localhost:8090';
  
  String path;
  if (channelId != null) {
    path = '$location/$ownerId/$channelId/$name.$extension';
  } else {
    path = '$location/$ownerId/$name.$extension';
  }
  
  return '$host/$bucketLocation/$bucketPermission/$bucketName/$path';
}
