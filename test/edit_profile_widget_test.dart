import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/screens/edit_profile_screen.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/models/user_model.dart';

// Mock AuthProvider for testing
class MockAuthProvider extends ChangeNotifier implements AuthProvider {
  UserModel? _user;
  String? _errorMessage;
  final bool _isAuthenticated = true;

  @override
  UserModel? get user => _user;

  @override
  String? get errorMessage => _errorMessage;

  @override
  bool get isAuthenticated => _isAuthenticated;

  @override
  bool get isLoading => false;

  @override
  AuthStatus get status => AuthStatus.authenticated;

  MockAuthProvider() {
    _user = UserModel(
      id: 'test-user-id',
      email: '<EMAIL>',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<bool> signIn({required String email, required String password}) async {
    // Mo<PERSON> sign in - always return true for testing
    return true;
  }

  @override
  Future<bool> changePassword(String newPassword) async {
    // Mock change password - always return true for testing
    return true;
  }

  @override
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Implement other required methods as no-ops for testing
  @override
  Future<bool> signUp({
    required String email,
    required String password,
  }) async => true;

  @override
  Future<void> signOut() async {}

  @override
  Future<bool> resetPassword(String email) async => true;
}

void main() {
  group('EditProfileScreen Widget Tests', () {
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = MockAuthProvider();
    });

    testWidgets('should display all password change form fields', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const EditProfileScreen(),
          ),
        ),
      );

      // Verify the screen title
      expect(find.text('Edit Profile'), findsOneWidget);

      // Verify the change password section title and button
      expect(
        find.text('Change Password'),
        findsNWidgets(2),
      ); // Title and button

      // Verify all password fields are present
      expect(find.text('Current Password'), findsOneWidget);
      expect(find.text('New Password'), findsOneWidget);
      expect(find.text('Confirm New Password'), findsOneWidget);

      // Verify password visibility toggle buttons
      expect(find.byIcon(Icons.visibility), findsNWidgets(3));
    });

    testWidgets('should toggle password visibility when eye icons are tapped', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const EditProfileScreen(),
          ),
        ),
      );

      // Initially, all passwords should be obscured (visibility icons shown)
      expect(find.byIcon(Icons.visibility), findsNWidgets(3));
      expect(find.byIcon(Icons.visibility_off), findsNothing);

      // Tap the first visibility icon (current password)
      await tester.tap(find.byIcon(Icons.visibility).first);
      await tester.pump();

      // Now we should have 2 visibility icons and 1 visibility_off icon
      expect(find.byIcon(Icons.visibility), findsNWidgets(2));
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should show validation errors for empty fields', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const EditProfileScreen(),
          ),
        ),
      );

      // Find and tap the change password button without filling fields
      final changePasswordButton = find.text('Change Password').last;
      await tester.tap(changePasswordButton);
      await tester.pump();

      // Verify validation error messages appear
      expect(find.text('Please enter your current password'), findsOneWidget);
      expect(find.text('Please enter a new password'), findsOneWidget);
      expect(find.text('Please confirm your new password'), findsOneWidget);
    });

    testWidgets('should show validation error when passwords do not match', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const EditProfileScreen(),
          ),
        ),
      );

      // Fill in the form with mismatched passwords
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Enter your current password'),
        'currentpass123',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Enter your new password'),
        'newpass123',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Confirm your new password'),
        'differentpass123',
      );

      // Tap the change password button
      final changePasswordButton = find.text('Change Password').last;
      await tester.tap(changePasswordButton);
      await tester.pump();

      // Verify password mismatch error appears
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('should show validation error for short password', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const EditProfileScreen(),
          ),
        ),
      );

      // Fill in the form with a short new password
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Enter your current password'),
        'currentpass123',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Enter your new password'),
        '123',
      );
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Confirm your new password'),
        '123',
      );

      // Tap the change password button
      final changePasswordButton = find.text('Change Password').last;
      await tester.tap(changePasswordButton);
      await tester.pump();

      // Verify short password error appears
      expect(
        find.text('Password must be at least 6 characters'),
        findsOneWidget,
      );
    });

    testWidgets(
      'should show validation error when new password is same as current',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: ChangeNotifierProvider<AuthProvider>.value(
              value: mockAuthProvider,
              child: const EditProfileScreen(),
            ),
          ),
        );

        // Fill in the form with same current and new password
        const samePassword = 'samepass123';
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Enter your current password'),
          samePassword,
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Enter your new password'),
          samePassword,
        );
        await tester.enterText(
          find.widgetWithText(TextFormField, 'Confirm your new password'),
          samePassword,
        );

        // Tap the change password button
        final changePasswordButton = find.text('Change Password').last;
        await tester.tap(changePasswordButton);
        await tester.pump();

        // Verify same password error appears
        expect(
          find.text('New password must be different from current password'),
          findsOneWidget,
        );
      },
    );
  });
}
