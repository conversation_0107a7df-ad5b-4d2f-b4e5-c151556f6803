// test/clipart_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:gameflex_mobile/models/clipart_model.dart';
import 'package:gameflex_mobile/services/clipart_service.dart';
import 'package:gameflex_mobile/widgets/clipart_selection_widget.dart';

void main() {
  group('ClipArt Tests', () {
    test('ClipArtService should return all categories', () {
      final service = ClipArtService();
      final categories = service.getCategories();
      
      expect(categories.length, equals(6));
      expect(categories, contains(ClipArtCategory.arrows));
      expect(categories, contains(ClipArtCategory.bubbles));
      expect(categories, contains(ClipArtCategory.flexFun));
      expect(categories, contains(ClipArtCategory.memes));
      expect(categories, contains(ClipArtCategory.smack));
      expect(categories, contains(ClipArtCategory.smellies));
    });

    test('ClipArtService should return items for each category', () {
      final service = ClipArtService();
      
      for (final category in ClipArtCategory.values) {
        final items = service.getClipArtForCategory(category);
        expect(items.isNotEmpty, isTrue, reason: 'Category ${category.displayName} should have items');
        
        for (final item in items) {
          expect(item.category, equals(category));
          expect(item.fileName.isNotEmpty, isTrue);
          expect(item.assetPath.contains(category.folderName), isTrue);
        }
      }
    });

    test('ClipArtItem should generate correct asset paths', () {
      final item = ClipArtItem(
        name: 'Test Arrow',
        fileName: 'Arrow 1.png',
        category: ClipArtCategory.arrows,
      );
      
      expect(item.assetPath, equals('assets/images/clipart/arrows/Arrow 1.png'));
      expect(item.displayName, equals('Arrow 1'));
    });

    test('ClipArtOverlay should support transformations', () {
      final item = ClipArtItem(
        name: 'Test',
        fileName: 'test.png',
        category: ClipArtCategory.arrows,
      );
      
      final overlay = ClipArtOverlay(
        item: item,
        x: 0.5,
        y: 0.5,
        scale: 1.0,
        rotation: 0.0,
        id: 'test-id',
      );
      
      final transformed = overlay.copyWith(
        x: 0.7,
        scale: 1.5,
        rotation: 0.5,
      );
      
      expect(transformed.x, equals(0.7));
      expect(transformed.y, equals(0.5)); // unchanged
      expect(transformed.scale, equals(1.5));
      expect(transformed.rotation, equals(0.5));
      expect(transformed.id, equals('test-id')); // unchanged
    });

    testWidgets('ClipArtSelectionWidget should display categories', (WidgetTester tester) async {
      bool clipArtSelected = false;
      ClipArtItem? selectedItem;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ClipArtSelectionWidget(
              onClipArtSelected: (item) {
                clipArtSelected = true;
                selectedItem = item;
              },
            ),
          ),
        ),
      );
      
      // Check if the widget displays the header
      expect(find.text('Add Clip Art'), findsOneWidget);
      
      // Check if category tabs are displayed
      expect(find.text('Arrows'), findsOneWidget);
      expect(find.text('Bubbles'), findsOneWidget);
      expect(find.text('Flex Fun'), findsOneWidget);
      expect(find.text('Memes'), findsOneWidget);
      expect(find.text('Smack'), findsOneWidget);
      expect(find.text('Smellies'), findsOneWidget);
    });

    test('ClipArtCategory extensions should work correctly', () {
      expect(ClipArtCategory.arrows.displayName, equals('Arrows'));
      expect(ClipArtCategory.flexFun.displayName, equals('Flex Fun'));
      expect(ClipArtCategory.arrows.folderName, equals('arrows'));
      expect(ClipArtCategory.flexFun.folderName, equals('flex_fun'));
      expect(ClipArtCategory.arrows.assetPath, equals('assets/images/clipart/arrows/'));
    });

    test('ClipArtService should find items by filename', () {
      final service = ClipArtService();
      
      final foundItem = service.findClipArtByFileName('Arrow 1.png');
      expect(foundItem, isNotNull);
      expect(foundItem!.fileName, equals('Arrow 1.png'));
      expect(foundItem.category, equals(ClipArtCategory.arrows));
      
      final notFound = service.findClipArtByFileName('nonexistent.png');
      expect(notFound, isNull);
    });
  });
}
