# GameFlex Mobile Build Guide

This guide explains how to build and run GameFlex Mobile for both development and production environments.

## Overview

The app supports two build configurations:

- **Development**: Uses local backend (localhost/********) for development and testing
- **Production**: Uses production backend (https://api.gameflex.io) for live deployment

## Quick Start

### Development Build & Run

```powershell
# Run in development mode (Windows)
.\run-dev.ps1

# Run in development mode (Android)
.\run-dev.ps1 -Platform android

# Run in development mode (iOS)
.\run-dev.ps1 -Platform ios

# Build for development (Windows)
.\build-dev.ps1

# Build for development (Android)
.\build-dev.ps1 -Platform android

# Build for development (iOS)
.\build-dev.ps1 -Platform ios
```

### Production Build & Run

```powershell
# Run in production mode (Windows)
.\run-prod.ps1

# Run in production mode (Android)
.\run-prod.ps1 -Platform android

# Run in production mode (iOS)
.\run-prod.ps1 -Platform ios

# Build for production (Windows)
.\build-prod.ps1

# Build for production (Android)
.\build-prod.ps1 -Platform android

# Build for production (iOS)
.\build-prod.ps1 -Platform ios
```

## Build Scripts

### Development Scripts

- **`run-dev.ps1`**: Runs the app in development mode with hot reload
- **`build-dev.ps1`**: Builds the app for development testing

### Production Scripts

- **`run-staging.ps1`**: Runs the app in staging mode (with confirmation prompt)
- **`build-staging.ps1`**: Builds the app for staging deployment

## Environment Configuration

### Development Environment

- **Backend URL**: `http://localhost:8090` (Windows/Web) or `http://********:8090` (Android)
- **App Name**: "GameFlex Dev" (Android)
- **Package ID**: `com.gameflex.app.dev` (Android/iOS)
- **Debug Logging**: Enabled
- **Build Mode**: Debug (default) or Release

### Staging Environment

- **Backend URL**: `http://dev.api.gameflex.io:8000`
- **App Name**: "GameFlex Staging"
- **Package ID**: `com.gameflex.app.staging`
- **Debug Logging**: Disabled
- **Build Mode**: Release only

## Platform Support

### Windows
- Development and production builds supported
- Uses localhost for development backend
- Build output: `build\windows\x64\runner\{debug|release}\`

### Android
- Development and production flavors configured
- Uses ******** for development backend (emulator compatibility)
- Build output: `build\app\outputs\flutter-apk\app-{flavor}-{mode}.apk`

### iOS
- Development and production configurations
- Separate bundle identifiers and app names
- Build output: `build\ios\iphoneos\Runner.app` or `build\ios\iphonesimulator\Runner.app`

### Web
- Development and production builds supported
- Uses localhost for development backend
- Build output: `build\web\`
- Runs on port 3001 to avoid conflicts

## Script Options

All scripts support the following options:

- `-Platform <platform>`: Target platform (windows, android, ios, web)
- `-Help`: Show help information

Development scripts also support:
- `-Release`: Build/run in release mode instead of debug

## Examples

```powershell
# Development Examples
.\run-dev.ps1                           # Run Windows debug
.\run-dev.ps1 -Platform android         # Run Android debug
.\run-dev.ps1 -Platform ios             # Run iOS debug
.\run-dev.ps1 -Platform android -Release # Run Android release (dev backend)
.\build-dev.ps1 -Platform web           # Build Web debug

# Production Examples
.\run-prod.ps1                          # Run Windows production
.\run-prod.ps1 -Platform android        # Run Android production
.\run-prod.ps1 -Platform ios            # Run iOS production
.\build-prod.ps1 -Platform web          # Build Web production
```

## Backend Requirements

### Development
- Local backend must be running on localhost:8090
- Use `backend/start.ps1` to start the development backend
- Ensure Docker Desktop is running

### Production
- Production backend must be accessible at http://api.gameflex.io:8000
- Ensure production servers are running and accessible
- Test connectivity before building/running

## Troubleshooting

### Common Issues

1. **"pubspec.yaml not found"**
   - Ensure you're running scripts from the Flutter project root directory

2. **Flutter command not found**
   - Ensure Flutter SDK is installed and in your PATH
   - Check `.vscode/settings.json` for Flutter SDK path

3. **Android build fails**
   - Ensure Android SDK is installed
   - Check that Android emulator/device is connected for run commands

4. **Backend connection fails**
   - For development: Ensure local backend is running (`backend/start.ps1`)
   - For production: Verify https://api.gameflex.io is accessible

### Build Clean

If you encounter build issues, try cleaning:

```powershell
flutter clean
flutter pub get
```

## Notes

- Production runs require confirmation to prevent accidental production connections
- Android development builds use a different package ID to allow side-by-side installation
- All production builds are release mode only for optimal performance
- Development builds default to debug mode but can be built in release mode for testing
