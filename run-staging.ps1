#!/usr/bin/env pwsh
# Staging Run Script for GameFlex Mobile
# This script runs the app in staging mode with dev.api.gameflex.io URLs

param(
    [string]$Platform = "windows",
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Staging Run Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\run-staging.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\run-staging.ps1                    # Run Windows staging"
    Write-Host "  .\run-staging.ps1 -Platform android  # Run Android staging"
    Write-Host "  .\run-staging.ps1 -Platform ios      # Run iOS staging"
    Write-Host "  .\run-staging.ps1 -Platform web      # Run Web staging"
    Write-Host ""
    Write-Host "⚠️  Warning: This connects to staging servers!" -ForegroundColor Red
    exit 0
}

Write-Host "🚀 Running GameFlex Mobile in STAGING mode" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: Release (Staging)" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

Write-Host "🔧 Staging Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses staging backend (https://dev.api.gameflex.io)" -ForegroundColor White
Write-Host "  - Release mode optimizations" -ForegroundColor White
Write-Host "  - Staging app name/icon" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Warning: This will connect to staging servers!" -ForegroundColor Red
Write-Host ""

# Confirm staging run
$confirmation = Read-Host "Are you sure you want to run in STAGING mode? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "❌ Staging run cancelled" -ForegroundColor Yellow
    exit 0
}

switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏃 Running on Windows (staging)..." -ForegroundColor Yellow
        flutter run -d windows --release --dart-define=STAGING=true
    }
    "android" {
        Write-Host "🏃 Running on Android (staging)..." -ForegroundColor Yellow
        flutter run -d android --release --flavor staging --dart-define=STAGING=true
    }
    "ios" {
        Write-Host "🏃 Running on iOS (staging)..." -ForegroundColor Yellow
        flutter run -d ios --release --dart-define=STAGING=true
    }
    "web" {
        Write-Host "🏃 Running on Web (staging)..." -ForegroundColor Yellow
        flutter run -d web-server --release --dart-define=STAGING=true --web-port 3001
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}
