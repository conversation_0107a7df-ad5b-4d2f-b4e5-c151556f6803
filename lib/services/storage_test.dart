import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_service.dart';

class StorageTest {
  static final SupabaseClient _supabase = SupabaseService.supabaseClient;

  /// Test if the media bucket exists and is accessible
  static Future<bool> testMediaBucket() async {
    try {
      print('Testing media bucket accessibility...');
      
      // Try to list objects in the media bucket
      final response = await _supabase.storage.from('media').list();
      
      print('✅ Media bucket is accessible');
      print('Found ${response.length} objects in media bucket');
      
      return true;
    } catch (e) {
      print('❌ Media bucket test failed: $e');
      
      // Check if it's a bucket not found error
      if (e.toString().contains('Bucket not found')) {
        print('💡 The media bucket does not exist. Please ensure your backend is running and the storage initialization completed.');
        print('💡 Run: docker-compose up in your backend directory');
      }
      
      return false;
    }
  }

  /// Test basic storage connectivity
  static Future<bool> testStorageConnectivity() async {
    try {
      print('Testing storage service connectivity...');
      
      // Try to list all buckets
      final buckets = await _supabase.storage.listBuckets();
      
      print('✅ Storage service is accessible');
      print('Available buckets:');
      for (final bucket in buckets) {
        print('  - ${bucket.name} (${bucket.public ? 'public' : 'private'})');
      }
      
      return true;
    } catch (e) {
      print('❌ Storage connectivity test failed: $e');
      print('💡 Please check if your backend is running and accessible');
      
      return false;
    }
  }

  /// Run all storage tests
  static Future<void> runAllTests() async {
    print('🧪 Running storage tests...\n');
    
    final connectivityOk = await testStorageConnectivity();
    print('');
    
    if (connectivityOk) {
      await testMediaBucket();
    } else {
      print('⚠️  Skipping bucket test due to connectivity issues');
    }
    
    print('\n🏁 Storage tests completed');
  }
}
