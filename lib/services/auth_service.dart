import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'supabase_service.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  SupabaseClient get _client => SupabaseService.instance.client;

  // Storage keys for JWT tokens
  static const String _accessTokenKey = 'supabase_access_token';
  static const String _refreshTokenKey = 'supabase_refresh_token';

  /// Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    try {
      developer.log('AuthService: Starting sign up for email: $email');
      if (kDebugMode) {
        print('AuthService: Starting sign up for email: $email');
      }

      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      developer.log(
        'AuthService: Sign up response received - User: ${response.user?.id}, Session: ${response.session != null}',
      );
      if (kDebugMode) {
        print(
          'AuthService: Sign up response - User: ${response.user?.id}, Session: ${response.session != null}',
        );
        print(
          'AuthService: Sign up response - User confirmed: ${response.user?.emailConfirmedAt != null}',
        );
      }

      if (response.session != null) {
        developer.log('AuthService: Saving tokens for sign up');
        if (kDebugMode) {
          print('AuthService: Saving tokens for sign up');
        }
        await _saveTokens(response.session!);
      }

      return response;
    } catch (e, stackTrace) {
      developer.log(
        'AuthService: Sign up failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthService: SIGN UP ERROR: $e');
        print('AuthService: SIGN UP STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      developer.log('AuthService: Starting sign in for email: $email');
      if (kDebugMode) {
        print('AuthService: Starting sign in for email: $email');
      }

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      developer.log(
        'AuthService: Sign in response received - User: ${response.user?.id}, Session: ${response.session != null}',
      );
      if (kDebugMode) {
        print(
          'AuthService: Sign in response - User: ${response.user?.id}, Session: ${response.session != null}',
        );
        print(
          'AuthService: Sign in response - User confirmed: ${response.user?.emailConfirmedAt != null}',
        );
      }

      if (response.session != null) {
        developer.log('AuthService: Saving tokens for sign in');
        if (kDebugMode) {
          print('AuthService: Saving tokens for sign in');
        }
        await _saveTokens(response.session!);
      }

      return response;
    } catch (e, stackTrace) {
      developer.log(
        'AuthService: Sign in failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthService: SIGN IN ERROR: $e');
        print('AuthService: SIGN IN STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      developer.log('AuthService: Starting sign out');
      if (kDebugMode) {
        print('AuthService: Starting sign out');
      }

      await _client.auth.signOut();
      await _clearTokens();

      developer.log('AuthService: Sign out completed successfully');
      if (kDebugMode) {
        print('AuthService: Sign out completed successfully');
      }
    } catch (e, stackTrace) {
      developer.log(
        'AuthService: Sign out failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthService: SIGN OUT ERROR: $e');
        print('AuthService: SIGN OUT STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  /// Get current user
  User? get currentUser => _client.auth.currentUser;

  /// Get current session
  Session? get currentSession => _client.auth.currentSession;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Get auth state changes stream
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  /// Save JWT tokens to local storage
  Future<void> _saveTokens(Session session) async {
    try {
      developer.log('AuthService: Saving tokens to local storage');
      if (kDebugMode) {
        print('AuthService: Saving tokens to local storage');
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, session.accessToken);
      if (session.refreshToken != null) {
        await prefs.setString(_refreshTokenKey, session.refreshToken!);
      }

      developer.log('AuthService: Tokens saved successfully');
      if (kDebugMode) {
        print('AuthService: Tokens saved successfully');
      }
    } catch (e, stackTrace) {
      developer.log(
        'AuthService: Failed to save tokens',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthService: SAVE TOKENS ERROR: $e');
      }
      rethrow;
    }
  }

  /// Clear JWT tokens from local storage
  Future<void> _clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
  }

  /// Get stored access token
  Future<String?> getStoredAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  /// Get stored refresh token
  Future<String?> getStoredRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  /// Restore session from stored tokens
  Future<bool> restoreSession() async {
    try {
      developer.log(
        'AuthService: Attempting to restore session from stored tokens',
      );
      if (kDebugMode) {
        print('AuthService: Attempting to restore session from stored tokens');
      }

      final accessToken = await getStoredAccessToken();
      final refreshToken = await getStoredRefreshToken();

      developer.log(
        'AuthService: Retrieved tokens - Access: ${accessToken != null}, Refresh: ${refreshToken != null}',
      );
      if (kDebugMode) {
        print(
          'AuthService: Retrieved tokens - Access: ${accessToken != null}, Refresh: ${refreshToken != null}',
        );
      }

      if (accessToken != null && refreshToken != null) {
        await _client.auth.setSession(refreshToken);
        developer.log('AuthService: Session restored successfully');
        if (kDebugMode) {
          print('AuthService: Session restored successfully');
        }
        return true;
      }

      developer.log(
        'AuthService: No valid tokens found for session restoration',
      );
      if (kDebugMode) {
        print('AuthService: No valid tokens found for session restoration');
      }
      return false;
    } catch (e, stackTrace) {
      developer.log(
        'AuthService: Session restoration failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthService: SESSION RESTORATION ERROR: $e');
        print('AuthService: SESSION RESTORATION STACK TRACE: $stackTrace');
      }
      // If session restoration fails, clear stored tokens
      await _clearTokens();
      return false;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  /// Update password for the current user
  Future<void> updatePassword(String newPassword) async {
    try {
      await _client.auth.updateUser(UserAttributes(password: newPassword));
    } catch (e) {
      rethrow;
    }
  }
}
