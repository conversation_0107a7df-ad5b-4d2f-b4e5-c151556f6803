import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_service.dart';

/// Helper class to construct media URLs from media records
class MediaUrlHelper {
  /// Construct full URL from media record
  static String constructMediaUrl(
    Map<String, dynamic> media, {
    String? baseUrl,
  }) {
    final location = media['location'] as String;
    final name = media['name'] as String;
    final extension = media['extension'] as String;
    final channelId = media['channel_id'] as String?;
    final ownerId = media['owner_id'] as String;
    final bucketLocation =
        media['bucket_location'] as String? ?? 'storage/v1/object';
    final bucketName = media['bucket_name'] as String? ?? 'media';
    final bucketPermission = media['bucket_permission'] as String? ?? 'public';

    // Use provided baseUrl or default to environment-aware URL
    final host = baseUrl ?? SupabaseService.supabaseUrl;

    // Construct path based on location and ownership
    String path;
    if (channelId != null) {
      path = '$location/$ownerId/$channelId/$name.$extension';
    } else {
      path = '$location/$ownerId/$name.$extension';
    }

    return '$host/$bucketLocation/$bucketPermission/$bucketName/$path';
  }
}

class UploadService {
  final SupabaseClient _supabase = SupabaseService.supabaseClient;
  static const String _bucketName = 'media';

  /// Create a complete post with image and text content
  Future<bool> createPost({
    required File imageFile,
    required String content,
    String? channelId,
  }) async {
    try {
      // Get current user
      final user = _supabase.auth.currentUser;
      if (user == null) {
        print('User not authenticated');
        return false;
      }

      // Generate unique filename and extract extension
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileExtension = _getFileExtension(imageFile.path);
      final fileName = '${user.id}_$timestamp.$fileExtension';

      // Create structured path: user/{owner_id}/{channel_id}/{filename} or user/{owner_id}/{filename}
      String filePath;
      if (channelId != null) {
        filePath = 'user/${user.id}/$channelId/$fileName';
      } else {
        filePath = 'user/${user.id}/$fileName';
      }

      // Read file bytes
      final bytes = await imageFile.readAsBytes();

      // Upload to Supabase storage (using media bucket)
      await _supabase.storage.from(_bucketName).uploadBinary(filePath, bytes);

      // Create media record first
      final mediaId = await _createMediaRecord(
        fileName: fileName.split('.').first, // filename without extension
        extension: fileExtension,
        type: _getMediaType(fileExtension),
        location: 'user',
        ownerId: user.id,
        channelId: channelId,
      );

      // Create post record in database with content
      await _createPostWithMedia(mediaId, user.id, content);

      print('Post created successfully with media ID: $mediaId');
      return true;
    } catch (e) {
      print('Error creating post: $e');

      // Provide more specific error messages
      if (e.toString().contains('Bucket not found')) {
        print(
          '💡 The media bucket does not exist. Please ensure your backend is running.',
        );
        print('💡 Run: docker-compose up in your backend directory');
      } else if (e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        print(
          '💡 Authentication error. Please check your Supabase configuration.',
        );
      } else if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        print(
          '💡 Network error. Please check your internet connection and backend URL.',
        );
      }

      return false;
    }
  }

  /// Upload an image file to Supabase storage (legacy method)
  Future<bool> uploadImage(File imageFile) async {
    return createPost(imageFile: imageFile, content: '');
  }

  /// Create a media record in the database
  Future<String> _createMediaRecord({
    required String fileName,
    required String extension,
    required String type,
    required String location,
    required String ownerId,
    String? channelId,
  }) async {
    try {
      final response =
          await _supabase
              .from('media')
              .insert({
                'type': type,
                'location': location,
                'name': fileName,
                'extension': extension,
                'channel_id': channelId,
                'owner_id': ownerId,
                'bucket_location': 'storage/v1/object',
                'bucket_name': _bucketName,
                'bucket_permission': 'public',
              })
              .select('id')
              .single();

      return response['id'] as String;
    } catch (e) {
      print('Error creating media record: $e');
      rethrow;
    }
  }

  /// Create a post record in the database with media reference
  Future<void> _createPostWithMedia(
    String mediaId,
    String userId,
    String content,
  ) async {
    try {
      await _supabase.from('posts').insert({
        'user_id': userId,
        'media_id': mediaId,
        'content': content,
        'channel_id': null, // Explicitly set to null for non-channel posts
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error creating post record: $e');
      rethrow;
    }
  }

  /// Get file extension from file path
  String _getFileExtension(String filePath) {
    final parts = filePath.split('.');
    if (parts.length > 1) {
      return parts.last.toLowerCase();
    }
    return 'jpg'; // default fallback
  }

  /// Get media type from file extension
  String _getMediaType(String extension) {
    switch (extension.toLowerCase()) {
      case 'mp4':
      case 'webm':
      case 'mov':
      case 'avi':
        return 'video';
      case 'gif':
        return 'gif';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
      default:
        return 'image';
    }
  }

  /// Create a post record in the database with content (legacy method)
  Future<void> _createPostWithContent(
    String imageUrl,
    String userId,
    String content,
  ) async {
    try {
      await _supabase.from('posts').insert({
        'user_id': userId,
        'media_url': imageUrl, // Legacy field - deprecated
        'media_type': 'image', // Legacy field - deprecated
        'content': content,
        'channel_id': null, // Explicitly set to null for non-channel posts
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error creating post record: $e');
      rethrow;
    }
  }

  /// Upload multiple images (for future use)
  Future<List<String>> uploadMultipleImages(List<File> imageFiles) async {
    final uploadedUrls = <String>[];

    for (final file in imageFiles) {
      try {
        final success = await uploadImage(file);
        if (success) {
          // In a real implementation, you'd return the actual URL
          uploadedUrls.add('uploaded_${DateTime.now().millisecondsSinceEpoch}');
        }
      } catch (e) {
        print('Error uploading file ${file.path}: $e');
      }
    }

    return uploadedUrls;
  }

  /// Delete an uploaded image
  Future<bool> deleteImage(String filePath) async {
    try {
      await _supabase.storage.from(_bucketName).remove([filePath]);

      return true;
    } catch (e) {
      print('Error deleting image: $e');
      return false;
    }
  }

  /// Get upload progress (for future implementation)
  Stream<double> getUploadProgress() {
    // This would be implemented with a proper upload progress tracking
    // For now, return a simple stream
    return Stream.periodic(
      const Duration(milliseconds: 100),
      (count) => (count * 10).clamp(0, 100).toDouble(),
    ).take(11);
  }
}
