import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_service.dart';

class UserService {
  static UserService? _instance;
  static UserService get instance => _instance ??= UserService._();

  UserService._();

  SupabaseClient get _client => SupabaseService.instance.client;

  /// Ensure user exists in public.users table
  /// This should be called after successful authentication
  Future<void> ensureUserExists(User authUser) async {
    try {
      developer.log('UserService: Ensuring user exists for: ${authUser.id}');

      // Check if user already exists in public.users by ID
      final existingUserById =
          await _client
              .from('users')
              .select('id, email')
              .eq('id', authUser.id)
              .maybeSingle();

      if (existingUserById != null) {
        developer.log(
          'UserService: User already exists in public.users with correct ID',
        );
        return;
      }

      // Check if user exists by email (in case of ID mismatch from old seeded data)
      if (authUser.email != null) {
        final existingUserByEmail =
            await _client
                .from('users')
                .select('id, email, username, display_name')
                .eq('email', authUser.email!)
                .maybeSingle();

        if (existingUserByEmail != null) {
          developer.log(
            'UserService: User exists with different ID (${existingUserByEmail['id']}), this is likely from seeded data. Creating new record with correct auth ID.',
          );

          // Instead of updating (which can cause foreign key issues),
          // create a new user record with the correct auth ID and a modified email/username
          final baseUsername = authUser.email!.split('@').first.toLowerCase();
          final baseEmail = authUser.email!;

          // Create unique username and email to avoid conflicts
          final newUsername = '${baseUsername}_${authUser.id.substring(0, 8)}';
          final newEmail =
              '${baseUsername}_${authUser.id.substring(0, 8)}@${baseEmail.split('@')[1]}';

          await _client.from('users').insert({
            'id': authUser.id,
            'email': newEmail,
            'username': newUsername,
            'display_name':
                existingUserByEmail['display_name'] ??
                authUser.userMetadata?['display_name'] ??
                baseUsername,
            'is_active': true,
            'is_verified': authUser.emailConfirmedAt != null,
            'created_at': authUser.createdAt,
            'updated_at': DateTime.now().toIso8601String(),
          });

          developer.log(
            'UserService: Successfully created user record with unique identifiers',
          );
          return;
        }
      }

      // Create user record in public.users table (no conflicts)
      developer.log('UserService: Creating new user record in public.users');

      // Extract display name from metadata or email
      final displayName =
          authUser.userMetadata?['display_name'] as String? ??
          authUser.userMetadata?['full_name'] as String? ??
          authUser.email?.split('@').first ??
          'User';

      final username =
          authUser.email?.split('@').first.toLowerCase() ??
          'user_${authUser.id.substring(0, 8)}';

      await _client.from('users').insert({
        'id': authUser.id,
        'email': authUser.email,
        'username': username,
        'display_name': displayName,
        'is_active': true,
        'is_verified': authUser.emailConfirmedAt != null,
        'created_at': authUser.createdAt,
        'updated_at': DateTime.now().toIso8601String(),
      });

      developer.log('UserService: Successfully created user record');
    } catch (e) {
      developer.log('UserService: Error ensuring user exists: $e');
      if (kDebugMode) {
        print('UserService ERROR: $e');
      }
      // Don't rethrow - this is a background operation
      // The user can still use the app even if this fails
    }
  }

  /// Update user profile information
  Future<void> updateUserProfile({
    required String userId,
    String? displayName,
    String? username,
    String? bio,
    String? avatarUrl,
    DateTime? dateOfBirth,
  }) async {
    try {
      final updates = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (displayName != null) updates['display_name'] = displayName;
      if (username != null) updates['username'] = username;
      if (bio != null) updates['bio'] = bio;
      if (avatarUrl != null) updates['avatar_url'] = avatarUrl;
      if (dateOfBirth != null) {
        updates['date_of_birth'] = dateOfBirth.toIso8601String();
      }

      await _client.from('users').update(updates).eq('id', userId);

      developer.log('UserService: Successfully updated user profile');
    } catch (e) {
      developer.log('UserService: Error updating user profile: $e');
      throw Exception('Failed to update user profile: $e');
    }
  }

  /// Get user profile by ID
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      final response =
          await _client
              .from('users')
              .select('*')
              .eq('id', userId)
              .maybeSingle();

      return response;
    } catch (e) {
      developer.log('UserService: Error getting user profile: $e');
      return null;
    }
  }

  /// Check if username is available
  Future<bool> isUsernameAvailable(
    String username, {
    String? excludeUserId,
  }) async {
    try {
      var query = _client
          .from('users')
          .select('id')
          .eq('username', username.toLowerCase());

      if (excludeUserId != null) {
        query = query.neq('id', excludeUserId);
      }

      final result = await query.maybeSingle();
      return result == null;
    } catch (e) {
      developer.log('UserService: Error checking username availability: $e');
      return false;
    }
  }

  /// Search users by username or display name
  Future<List<Map<String, dynamic>>> searchUsers(
    String query, {
    int limit = 10,
  }) async {
    try {
      final response = await _client
          .from('users')
          .select('id, username, display_name, avatar_url')
          .or('username.ilike.%$query%,display_name.ilike.%$query%')
          .eq('is_active', true)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      developer.log('UserService: Error searching users: $e');
      return [];
    }
  }

  /// Get user statistics (posts, followers, etc.)
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      // Get post count by counting the actual posts
      final posts = await _client
          .from('posts')
          .select('id')
          .eq('user_id', userId)
          .eq('is_active', true);

      // Get likes count (posts liked by this user)
      final likes = await _client
          .from('likes')
          .select('id')
          .eq('user_id', userId)
          .not('post_id', 'is', null);

      return {
        'posts': posts.length,
        'followers': 0, // TODO: Implement when follows table is created
        'following': 0, // TODO: Implement when follows table is created
        'likes': likes.length,
      };
    } catch (e) {
      developer.log('UserService: Error getting user stats: $e');
      return {'posts': 0, 'followers': 0, 'following': 0, 'likes': 0};
    }
  }
}
