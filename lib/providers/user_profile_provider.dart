import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../services/posts_service.dart';
import '../services/user_service.dart';

enum UserProfileStatus { initial, loading, loaded, error }

class UserProfileProvider extends ChangeNotifier {
  UserProfileStatus _status = UserProfileStatus.initial;
  String? _error;
  Map<String, dynamic>? _userProfile;
  List<PostModel> _userPosts = [];
  Map<String, int> _userStats = {};
  bool _hasMorePosts = true;
  int _currentOffset = 0;
  final int _pageSize = 20;
  String? _currentUserId;

  // Getters
  UserProfileStatus get status => _status;
  String? get error => _error;
  Map<String, dynamic>? get userProfile => _userProfile;
  List<PostModel> get userPosts => _userPosts;
  Map<String, int> get userStats => _userStats;
  bool get hasMorePosts => _hasMorePosts;
  bool get isLoading => _status == UserProfileStatus.loading;

  /// Load user profile and their posts
  Future<void> loadUserProfile(String userId) async {
    if (_currentUserId == userId && _status == UserProfileStatus.loaded) {
      // Already loaded this user's profile
      return;
    }

    _setStatus(UserProfileStatus.loading);
    _currentUserId = userId;
    _currentOffset = 0;
    _hasMorePosts = true;
    _userPosts.clear();

    try {
      developer.log('UserProfileProvider: Loading profile for user $userId');

      // Load user profile data
      final profile = await UserService.instance.getUserProfile(userId);
      if (profile == null) {
        throw Exception('User profile not found');
      }

      // Load user statistics
      final stats = await UserService.instance.getUserStats(userId);

      // Load user posts
      final posts = await PostsService.instance.getUserPosts(
        userId,
        limit: _pageSize,
        offset: 0,
      );

      _userProfile = profile;
      _userStats = stats;
      _userPosts = posts;
      _currentOffset = posts.length;
      _hasMorePosts = posts.length == _pageSize;

      developer.log(
        'UserProfileProvider: Loaded profile with ${posts.length} posts',
      );
      _setStatus(UserProfileStatus.loaded);
    } catch (e, stackTrace) {
      developer.log(
        'UserProfileProvider: Error loading user profile',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('UserProfileProvider ERROR: $e');
        print('UserProfileProvider STACK TRACE: $stackTrace');
      }
      _setError('Failed to load user profile: $e');
    }
  }

  /// Load more posts for the current user
  Future<void> loadMorePosts() async {
    if (!_hasMorePosts || 
        _status == UserProfileStatus.loading || 
        _currentUserId == null) {
      return;
    }

    try {
      developer.log(
        'UserProfileProvider: Loading more posts for user $_currentUserId',
      );

      final morePosts = await PostsService.instance.getUserPosts(
        _currentUserId!,
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _userPosts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMorePosts = morePosts.length == _pageSize;
        notifyListeners();
      } else {
        _hasMorePosts = false;
        notifyListeners();
      }

      developer.log(
        'UserProfileProvider: Loaded ${morePosts.length} more posts',
      );
    } catch (e) {
      developer.log('UserProfileProvider: Error loading more posts: $e');
      _setError('Failed to load more posts: $e');
    }
  }

  /// Refresh user profile and posts
  Future<void> refreshProfile() async {
    if (_currentUserId == null) return;
    
    _currentOffset = 0;
    _hasMorePosts = true;
    await loadUserProfile(_currentUserId!);
  }

  /// Clear current profile data
  void clearProfile() {
    _userProfile = null;
    _userPosts.clear();
    _userStats.clear();
    _currentUserId = null;
    _currentOffset = 0;
    _hasMorePosts = true;
    _setStatus(UserProfileStatus.initial);
  }

  void _setStatus(UserProfileStatus status) {
    _status = status;
    _error = null;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _status = UserProfileStatus.error;
    notifyListeners();
  }
}
