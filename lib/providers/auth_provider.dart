import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/user_service.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  StreamSubscription<AuthState>? _authSubscription;

  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // Try to restore session from stored tokens
      final sessionRestored = await AuthService.instance.restoreSession();

      if (sessionRestored && AuthService.instance.isAuthenticated) {
        final supabaseUser = AuthService.instance.currentUser;
        if (supabaseUser != null) {
          _user = UserModel.fromSupabaseUser(supabaseUser);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(supabaseUser);
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }

      // Listen to auth state changes
      _authSubscription = AuthService.instance.authStateChanges.listen(
        _onAuthStateChange,
        onError: (error) {
          _setError('Authentication error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  /// Handle auth state changes
  void _onAuthStateChange(AuthState authState) {
    switch (authState.event) {
      case AuthChangeEvent.initialSession:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(authState.session!.user);
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
        break;
      case AuthChangeEvent.signedIn:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          _setStatus(AuthStatus.authenticated);
          // Ensure user record exists in public.users table (background operation)
          UserService.instance.ensureUserExists(authState.session!.user);
        }
        break;
      case AuthChangeEvent.signedOut:
        _user = null;
        _setStatus(AuthStatus.unauthenticated);
        break;
      case AuthChangeEvent.userUpdated:
        if (authState.session?.user != null) {
          _user = UserModel.fromSupabaseUser(authState.session!.user);
          notifyListeners();
        }
        break;
      case AuthChangeEvent.passwordRecovery:
        // Handle password recovery if needed
        break;
      case AuthChangeEvent.tokenRefreshed:
        // Token refreshed, no action needed
        break;
      case AuthChangeEvent.mfaChallengeVerified:
        // Handle MFA if needed
        break;
      default:
        // Handle any other auth events
        break;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign up process for email: $email');
    if (kDebugMode) {
      print('AuthProvider: Starting sign up process for email: $email');
    }

    _setStatus(AuthStatus.loading);

    try {
      final response = await AuthService.instance.signUp(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign up response received - User: ${response.user?.id}, Session: ${response.session != null}',
      );
      if (kDebugMode) {
        print(
          'AuthProvider: Sign up response - User: ${response.user?.id}, Session: ${response.session != null}',
        );
        print(
          'AuthProvider: User email confirmed: ${response.user?.emailConfirmedAt != null}',
        );
      }

      if (response.user != null) {
        developer.log('AuthProvider: Creating user record in database');
        if (kDebugMode) {
          print('AuthProvider: Creating user record in database');
        }

        // Ensure user record exists in public.users table
        await UserService.instance.ensureUserExists(response.user!);

        _user = UserModel.fromSupabaseUser(response.user!);
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Sign up completed successfully');
        if (kDebugMode) {
          print('AuthProvider: Sign up completed successfully');
        }
        return true;
      } else {
        final errorMsg = 'Sign up failed: No user returned';
        developer.log('AuthProvider: $errorMsg');
        if (kDebugMode) {
          print('AuthProvider: $errorMsg');
        }
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } on AuthException catch (e, stackTrace) {
      final errorMsg = 'Sign up failed: ${e.message}';
      developer.log(
        'AuthProvider: AuthException during sign up',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN UP AUTH ERROR: ${e.message}');
        print('AuthProvider: SIGN UP AUTH ERROR CODE: ${e.statusCode}');
        print('AuthProvider: SIGN UP AUTH STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    } catch (e, stackTrace) {
      final errorMsg = 'Sign up failed: $e';
      developer.log(
        'AuthProvider: Unexpected error during sign up',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN UP UNEXPECTED ERROR: $e');
        print('AuthProvider: SIGN UP UNEXPECTED STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign in process for email: $email');
    if (kDebugMode) {
      print('AuthProvider: Starting sign in process for email: $email');
    }

    _setStatus(AuthStatus.loading);

    try {
      final response = await AuthService.instance.signIn(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign in response received - User: ${response.user?.id}, Session: ${response.session != null}',
      );
      if (kDebugMode) {
        print(
          'AuthProvider: Sign in response - User: ${response.user?.id}, Session: ${response.session != null}',
        );
        print(
          'AuthProvider: User email confirmed: ${response.user?.emailConfirmedAt != null}',
        );
      }

      if (response.user != null) {
        developer.log('AuthProvider: Ensuring user record exists in database');
        if (kDebugMode) {
          print('AuthProvider: Ensuring user record exists in database');
        }

        // Ensure user record exists in public.users table
        await UserService.instance.ensureUserExists(response.user!);

        _user = UserModel.fromSupabaseUser(response.user!);
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Sign in completed successfully');
        if (kDebugMode) {
          print('AuthProvider: Sign in completed successfully');
        }
        return true;
      } else {
        final errorMsg = 'Sign in failed: No user returned';
        developer.log('AuthProvider: $errorMsg');
        if (kDebugMode) {
          print('AuthProvider: $errorMsg');
        }
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } on AuthException catch (e, stackTrace) {
      final errorMsg = 'Sign in failed: ${e.message}';
      developer.log(
        'AuthProvider: AuthException during sign in',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN IN AUTH ERROR: ${e.message}');
        print('AuthProvider: SIGN IN AUTH ERROR CODE: ${e.statusCode}');
        print('AuthProvider: SIGN IN AUTH STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    } catch (e, stackTrace) {
      final errorMsg = 'Sign in failed: $e';
      developer.log(
        'AuthProvider: Unexpected error during sign in',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN IN UNEXPECTED ERROR: $e');
        print('AuthProvider: SIGN IN UNEXPECTED STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setStatus(AuthStatus.loading);

    try {
      await AuthService.instance.signOut();
      _user = null;
      _setStatus(AuthStatus.unauthenticated);
    } catch (e) {
      _setError('Sign out failed: $e');
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await AuthService.instance.resetPassword(email);
      return true;
    } on AuthException catch (e) {
      _setError('Password reset failed: ${e.message}');
      return false;
    } catch (e) {
      _setError('Password reset failed: $e');
      return false;
    }
  }

  /// Change password for the current user
  Future<bool> changePassword(String newPassword) async {
    try {
      await AuthService.instance.updatePassword(newPassword);
      return true;
    } on AuthException catch (e) {
      _setError('Password change failed: ${e.message}');
      return false;
    } catch (e) {
      _setError('Password change failed: $e');
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  /// Set error without changing status (for login/signup failures)
  void _setErrorWithoutStatusChange(String error) {
    _errorMessage = error;
    // Reset loading status back to unauthenticated if we're currently loading
    if (_status == AuthStatus.loading) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
