import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post_model.dart';
import '../services/posts_service.dart';
import '../services/supabase_service.dart';

enum PostsStatus { initial, loading, loaded, error, refreshing }

class PostsProvider extends ChangeNotifier {
  PostsStatus _status = PostsStatus.initial;
  List<PostModel> _posts = [];
  String? _errorMessage;
  bool _hasMore = true;
  int _currentOffset = 0;
  static const int _pageSize = 20;

  // Current post index for maintaining scroll position
  int _currentPostIndex = 0;

  // Real-time subscriptions
  RealtimeChannel? _postsSubscription;
  RealtimeChannel? _likesSubscription;

  PostsStatus get status => _status;
  List<PostModel> get posts => _posts;
  String? get errorMessage => _errorMessage;
  bool get hasMore => _hasMore;
  bool get isLoading => _status == PostsStatus.loading;
  bool get isRefreshing => _status == PostsStatus.refreshing;
  int get currentPostIndex => _currentPostIndex;

  /// Update the current post index to maintain scroll position
  void setCurrentPostIndex(int index) {
    if (index >= 0 && index < _posts.length && _currentPostIndex != index) {
      _currentPostIndex = index;
      notifyListeners();
    }
  }

  /// Load initial posts
  Future<void> loadPosts() async {
    if (_status == PostsStatus.loading) return;

    developer.log('PostsProvider: Starting to load posts');
    _setStatus(PostsStatus.loading);
    _currentOffset = 0;
    _hasMore = true;

    try {
      // Test connection first
      developer.log('PostsProvider: Testing Supabase connection...');
      final connectionOk = await SupabaseService.instance.testConnection();
      if (!connectionOk) {
        throw Exception('Database connection failed');
      }

      developer.log('PostsProvider: Calling PostsService.getPosts()');
      final posts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      developer.log('PostsProvider: Received ${posts.length} posts');
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on initial load
      _setStatus(PostsStatus.loaded);
      developer.log('PostsProvider: Successfully loaded posts');
    } catch (e, stackTrace) {
      developer.log(
        'PostsProvider: Error loading posts',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('PostsProvider ERROR: $e');
        print('PostsProvider STACK TRACE: $stackTrace');
      }
      _setError('Failed to load posts: $e');
    }
  }

  /// Refresh posts (pull to refresh)
  Future<void> refreshPosts() async {
    if (_status == PostsStatus.refreshing) return;

    developer.log('PostsProvider: Starting to refresh posts');
    if (kDebugMode) {
      print(
        '🔄 PostsProvider: REFRESH TRIGGERED - Refreshing posts to get latest content',
      );
    }
    _setStatus(PostsStatus.refreshing);
    _currentOffset = 0;
    _hasMore = true;

    try {
      final posts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: 0,
      );

      developer.log('PostsProvider: Refreshed with ${posts.length} posts');
      _posts = posts;
      _currentOffset = posts.length;
      _hasMore = posts.length == _pageSize;
      _currentPostIndex = 0; // Reset to first post on refresh
      _setStatus(PostsStatus.loaded);
    } catch (e, stackTrace) {
      developer.log(
        'PostsProvider: Error refreshing posts',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('PostsProvider REFRESH ERROR: $e');
        print('PostsProvider REFRESH STACK TRACE: $stackTrace');
      }
      _setError('Failed to refresh posts: $e');
    }
  }

  /// Load more posts (pagination)
  Future<void> loadMorePosts() async {
    if (!_hasMore || _status == PostsStatus.loading) return;

    try {
      final morePosts = await PostsService.instance.getPosts(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (morePosts.isNotEmpty) {
        _posts.addAll(morePosts);
        _currentOffset += morePosts.length;
        _hasMore = morePosts.length == _pageSize;
        notifyListeners();
      } else {
        _hasMore = false;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to load more posts: $e');
    }
  }

  /// Like/unlike a post
  Future<void> toggleLike(String postId) async {
    try {
      developer.log('PostsProvider: Toggling like for post: $postId');

      // Find the post first
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex == -1) {
        developer.log('PostsProvider: Post not found in local list: $postId');
        return;
      }

      final post = _posts[postIndex];
      final wasLiked = post.isLikedByCurrentUser;

      // Optimistically update the UI first for immediate feedback
      final optimisticPost = post.copyWith(
        likeCount: wasLiked ? post.likeCount - 1 : post.likeCount + 1,
        isLikedByCurrentUser: !wasLiked,
      );
      _posts[postIndex] = optimisticPost;
      notifyListeners();

      developer.log(
        'PostsProvider: Optimistically updated post. Was liked: $wasLiked, New count: ${optimisticPost.likeCount}',
      );

      // Then make the actual API call
      final isLiked = await PostsService.instance.likePost(postId);
      developer.log('PostsProvider: API call completed. Is liked: $isLiked');

      // Update with the actual result from the server
      final finalPost = post.copyWith(
        likeCount: isLiked ? post.likeCount + 1 : post.likeCount - 1,
        isLikedByCurrentUser: isLiked,
      );
      _posts[postIndex] = finalPost;
      notifyListeners();

      developer.log(
        'PostsProvider: Final update completed. Like count: ${finalPost.likeCount}, Is liked: ${finalPost.isLikedByCurrentUser}',
      );
    } catch (e) {
      developer.log('PostsProvider: Error toggling like: $e');

      // Revert the optimistic update on error
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        // For now, just refresh all posts to get the correct state
        // In a production app, you might want to implement a single post refresh
        loadPosts();
      }

      _setError('Failed to like post: $e');
    }
  }

  /// Add a new post to the beginning of the list
  void addPost(PostModel post) {
    _posts.insert(0, post);
    notifyListeners();
  }

  /// Remove a post from the list
  void removePost(String postId) {
    _posts.removeWhere((post) => post.id == postId);
    notifyListeners();
  }

  /// Update a post in the list
  void updatePost(PostModel updatedPost) {
    final index = _posts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _posts[index] = updatedPost;
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    if (_status == PostsStatus.error) {
      _setStatus(PostsStatus.loaded);
    }
  }

  /// Set status and notify listeners
  void _setStatus(PostsStatus status) {
    _status = status;
    if (status != PostsStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    developer.log('PostsProvider: Setting error: $error');
    if (kDebugMode) {
      print('PostsProvider: ERROR SET: $error');
    }
    _errorMessage = error;
    _status = PostsStatus.error;
    notifyListeners();
  }

  /// Reset the provider state
  void reset() {
    _status = PostsStatus.initial;
    _posts.clear();
    _errorMessage = null;
    _hasMore = true;
    _currentOffset = 0;
    notifyListeners();
  }

  /// Start real-time subscriptions
  void startRealtimeSubscriptions() {
    try {
      final client = SupabaseService.instance.client;

      // Subscribe to posts table changes
      _postsSubscription =
          client
              .channel('posts_changes')
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'posts',
                callback: _handlePostsChange,
              )
              .subscribe();

      // Subscribe to likes table changes
      _likesSubscription =
          client
              .channel('likes_changes')
              .onPostgresChanges(
                event: PostgresChangeEvent.all,
                schema: 'public',
                table: 'likes',
                callback: _handleLikesChange,
              )
              .subscribe();

      developer.log('PostsProvider: Real-time subscriptions started');
    } catch (e) {
      developer.log(
        'PostsProvider: Failed to start real-time subscriptions: $e',
      );
    }
  }

  /// Stop real-time subscriptions
  void stopRealtimeSubscriptions() {
    try {
      _postsSubscription?.unsubscribe();
      _likesSubscription?.unsubscribe();
      _postsSubscription = null;
      _likesSubscription = null;
      developer.log('PostsProvider: Real-time subscriptions stopped');
    } catch (e) {
      developer.log(
        'PostsProvider: Error stopping real-time subscriptions: $e',
      );
    }
  }

  /// Handle posts table changes
  void _handlePostsChange(PostgresChangePayload payload) {
    try {
      developer.log(
        'PostsProvider: Posts change detected: ${payload.eventType}',
      );

      switch (payload.eventType) {
        case PostgresChangeEvent.insert:
          _handlePostInsert(payload.newRecord);
          break;
        case PostgresChangeEvent.update:
          _handlePostUpdate(payload.newRecord);
          break;
        case PostgresChangeEvent.delete:
          _handlePostDelete(payload.oldRecord);
          break;
        case PostgresChangeEvent.all:
          // Handle all events - this shouldn't happen in practice
          break;
      }
    } catch (e) {
      developer.log('PostsProvider: Error handling posts change: $e');
    }
  }

  /// Handle likes table changes
  void _handleLikesChange(PostgresChangePayload payload) {
    try {
      developer.log(
        'PostsProvider: Likes change detected: ${payload.eventType}',
      );

      final postId = payload.newRecord['post_id'] as String?;
      if (postId == null) return;

      // Find the post and update its like count
      final postIndex = _posts.indexWhere((post) => post.id == postId);
      if (postIndex != -1) {
        // Refresh the specific post to get updated like count
        _refreshPostLikeCount(postId);
      }
    } catch (e) {
      developer.log('PostsProvider: Error handling likes change: $e');
    }
  }

  /// Handle new post insertion
  void _handlePostInsert(Map<String, dynamic> record) {
    // For now, we'll just refresh the feed to get the new post
    // In a more sophisticated implementation, we could construct the PostModel
    // from the record and insert it directly
    loadPosts();
  }

  /// Handle post update
  void _handlePostUpdate(Map<String, dynamic> record) {
    final postId = record['id'] as String?;
    if (postId == null) return;

    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex != -1) {
      // Update the post with new data
      final updatedPost = _posts[postIndex].copyWith(
        content: record['content'] as String?,
        likeCount: record['like_count'] as int?,
        commentCount: record['comment_count'] as int?,
        isActive: record['is_active'] as bool?,
      );
      _posts[postIndex] = updatedPost;
      notifyListeners();
    }
  }

  /// Handle post deletion
  void _handlePostDelete(Map<String, dynamic> record) {
    final postId = record['id'] as String?;
    if (postId == null) return;

    _posts.removeWhere((post) => post.id == postId);
    notifyListeners();
  }

  /// Refresh like count for a specific post
  Future<void> _refreshPostLikeCount(String postId) async {
    try {
      final client = SupabaseService.instance.client;
      final response =
          await client
              .from('posts')
              .select('like_count')
              .eq('id', postId)
              .single();

      final newLikeCount = response['like_count'] as int;
      final postIndex = _posts.indexWhere((post) => post.id == postId);

      if (postIndex != -1) {
        final updatedPost = _posts[postIndex].copyWith(likeCount: newLikeCount);
        _posts[postIndex] = updatedPost;
        notifyListeners();
      }
    } catch (e) {
      developer.log('PostsProvider: Error refreshing post like count: $e');
    }
  }

  @override
  void dispose() {
    stopRealtimeSubscriptions();
    super.dispose();
  }
}
