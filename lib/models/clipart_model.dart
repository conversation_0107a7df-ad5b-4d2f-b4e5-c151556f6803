// lib/models/clipart_model.dart

enum ClipArtCategory {
  arrows,
  bubbles,
  flexFun,
  memes,
  smack,
  smellies,
}

extension ClipArtCategoryExtension on ClipArtCategory {
  String get displayName {
    switch (this) {
      case ClipArtCategory.arrows:
        return 'Arrows';
      case ClipArtCategory.bubbles:
        return 'Bubbles';
      case ClipArtCategory.flexFun:
        return 'Flex Fun';
      case ClipArtCategory.memes:
        return 'Memes';
      case ClipArtCategory.smack:
        return 'Smack';
      case ClipArtCategory.smellies:
        return 'Smellies';
    }
  }

  String get folderName {
    switch (this) {
      case ClipArtCategory.arrows:
        return 'arrows';
      case ClipArtCategory.bubbles:
        return 'bubbles';
      case ClipArtCategory.flexFun:
        return 'flex_fun';
      case ClipArtCategory.memes:
        return 'memes';
      case ClipArtCategory.smack:
        return 'smack';
      case ClipArtCategory.smellies:
        return 'smellies';
    }
  }

  String get assetPath {
    return 'assets/images/clipart/${folderName}/';
  }
}

class ClipArtItem {
  final String name;
  final String fileName;
  final ClipArtCategory category;

  ClipArtItem({
    required this.name,
    required this.fileName,
    required this.category,
  });

  String get assetPath {
    return '${category.assetPath}$fileName';
  }

  String get displayName {
    // Remove file extension and clean up the name
    String cleanName = fileName.replaceAll('.png', '');
    // Replace underscores and hyphens with spaces
    cleanName = cleanName.replaceAll('_', ' ').replaceAll('-', ' ');
    // Capitalize first letter of each word
    return cleanName.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClipArtItem &&
        other.fileName == fileName &&
        other.category == category;
  }

  @override
  int get hashCode => fileName.hashCode ^ category.hashCode;

  @override
  String toString() {
    return 'ClipArtItem(name: $name, fileName: $fileName, category: ${category.displayName})';
  }
}

class ClipArtOverlay {
  final ClipArtItem item;
  final double x;
  final double y;
  final double scale;
  final double rotation;
  final String id;

  ClipArtOverlay({
    required this.item,
    required this.x,
    required this.y,
    this.scale = 1.0,
    this.rotation = 0.0,
    required this.id,
  });

  ClipArtOverlay copyWith({
    ClipArtItem? item,
    double? x,
    double? y,
    double? scale,
    double? rotation,
    String? id,
  }) {
    return ClipArtOverlay(
      item: item ?? this.item,
      x: x ?? this.x,
      y: y ?? this.y,
      scale: scale ?? this.scale,
      rotation: rotation ?? this.rotation,
      id: id ?? this.id,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClipArtOverlay && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ClipArtOverlay(item: ${item.displayName}, x: $x, y: $y, scale: $scale, rotation: $rotation)';
  }
}
