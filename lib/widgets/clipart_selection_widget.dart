// lib/widgets/clipart_selection_widget.dart

import 'package:flutter/material.dart';
import '../models/clipart_model.dart';
import '../services/clipart_service.dart';
import '../theme/app_theme.dart';

class ClipArtSelectionWidget extends StatefulWidget {
  final Function(ClipArtItem) onClipArtSelected;

  const ClipArtSelectionWidget({super.key, required this.onClipArtSelected});

  @override
  State<ClipArtSelectionWidget> createState() => _ClipArtSelectionWidgetState();
}

class _ClipArtSelectionWidgetState extends State<ClipArtSelectionWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ClipArtService _clipArtService = ClipArtService();
  late List<ClipArtCategory> _categories;

  @override
  void initState() {
    super.initState();
    _categories = _clipArtService.getCategories();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.gfGrayText,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text(
                  'Add Clip Art',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.gfOffWhite,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: AppColors.gfOffWhite),
                ),
              ],
            ),
          ),

          // Category tabs
          Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.gfGrayText, width: 0.5),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: AppColors.gfGreen,
              labelColor: AppColors.gfGreen,
              unselectedLabelColor: AppColors.gfGrayText,
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
              tabs:
                  _categories.map((category) {
                    return Tab(text: category.displayName);
                  }).toList(),
            ),
          ),

          // Clip art grid
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children:
                  _categories.map((category) {
                    return _buildClipArtGrid(category);
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClipArtGrid(ClipArtCategory category) {
    final clipArtItems = _clipArtService.getClipArtForCategory(category);

    if (clipArtItems.isEmpty) {
      return const Center(
        child: Text(
          'No clip art available',
          style: TextStyle(color: AppColors.gfGrayText, fontSize: 16),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: clipArtItems.length,
        itemBuilder: (context, index) {
          final clipArtItem = clipArtItems[index];
          return _buildClipArtTile(clipArtItem);
        },
      ),
    );
  }

  Widget _buildClipArtTile(ClipArtItem clipArtItem) {
    return GestureDetector(
      onTap: () {
        widget.onClipArtSelected(clipArtItem);
        Navigator.of(context).pop();
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.gfGrayText.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // Clip art image
              Positioned.fill(
                child: Image.asset(
                  clipArtItem.assetPath,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    print('Error loading clip art: ${clipArtItem.assetPath}');
                    return Container(
                      color: AppColors.gfDarkBackground100,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppColors.gfGrayText,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),

              // Hover effect
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      widget.onClipArtSelected(clipArtItem);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Helper function to show the clip art selection bottom sheet
void showClipArtSelection(
  BuildContext context,
  Function(ClipArtItem) onClipArtSelected,
) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder:
        (context) =>
            ClipArtSelectionWidget(onClipArtSelected: onClipArtSelected),
  );
}
