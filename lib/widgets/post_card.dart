import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';

class PostCard extends StatelessWidget {
  final PostModel post;
  final VoidCallback? onTap;
  final bool showFullContent;

  const PostCard({
    super.key,
    required this.post,
    this.onTap,
    this.showFullContent = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppColors.gfDarkBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info header
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  _buildAvatar(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.authorDisplayName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              post.authorUsername,
                              style: const TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                            const Text(
                              ' • ',
                              style: TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              post.timeAgo,
                              style: const TextStyle(
                                color: AppColors.gfGrayText,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.more_vert,
                      color: AppColors.gfGrayText,
                    ),
                    onPressed: () => _showPostOptions(context),
                  ),
                ],
              ),
            ),

            // Post content
            if (post.content.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Text(
                  post.content,
                  style: const TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: showFullContent ? null : 6,
                  overflow:
                      showFullContent
                          ? TextOverflow.visible
                          : TextOverflow.ellipsis,
                ),
              ),

            // Media content
            if (post.hasMedia) _buildMediaContent(),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  // Debug info (remove in production)
                  if (kDebugMode)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        'DEBUG: Liked: ${post.isLikedByCurrentUser}, Count: ${post.likeCount}',
                        style: const TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildActionButton(
                        icon: Icons.shield_outlined,
                        activeIcon: Icons.shield,
                        label: post.likeCount.toString(),
                        isActive: post.isLikedByCurrentUser,
                        onPressed: () => _handleLike(context),
                      ),
                      _buildActionButton(
                        icon: Icons.chat_bubble_outline,
                        label: post.commentCount.toString(),
                        onPressed: () => _handleComment(context),
                      ),
                      _buildActionButton(
                        icon: Icons.share,
                        label: 'Share',
                        onPressed: () => _handleShare(context),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (post.avatarUrl != null && post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            post.avatarUrl!.isEmpty
                ? Text(
                  post.authorDisplayName.isNotEmpty
                      ? post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        post.authorDisplayName.isNotEmpty
            ? post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildMediaContent() {
    if (post.isImage) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        height: 250,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child:
              post.mediaUrl != null
                  ? Image.network(
                    post.mediaUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildMediaPlaceholder('Image');
                    },
                  )
                  : _buildMediaPlaceholder('Image'),
        ),
      );
    } else if (post.isVideo) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        height: 250,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: _buildMediaPlaceholder('Video'),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 48,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 8),
            Text(
              '$type Content',
              style: const TextStyle(color: AppColors.gfGrayText, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    IconData? activeIcon,
    required String label,
    bool isActive = false,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive && activeIcon != null ? activeIcon : icon,
              color: isActive ? AppColors.gfGreen : AppColors.gfGrayText,
              size: 20,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive ? AppColors.gfGreen : AppColors.gfGrayText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);

    // Show immediate visual feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          post.isLikedByCurrentUser ? 'Unliked post' : 'Liked post',
          style: const TextStyle(color: AppColors.gfOffWhite),
        ),
        backgroundColor: AppColors.gfTeal,
        duration: const Duration(milliseconds: 1500),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );

    // Trigger the like toggle
    await postsProvider.toggleLike(post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }

  void _handleShare(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }
}
