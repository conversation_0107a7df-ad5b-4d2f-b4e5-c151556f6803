import 'package:flutter/material.dart';
import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../models/comment_model.dart';
import '../services/comments_service.dart';
import '../widgets/post_card.dart';
import '../widgets/comment_card.dart';
import '../widgets/add_comment_widget.dart';
import '../theme/app_theme.dart';

class PostDetailScreen extends StatefulWidget {
  final PostModel post;

  const PostDetailScreen({
    Key? key,
    required this.post,
  }) : super(key: key);

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  List<CommentModel> _comments = [];
  bool _isLoading = true;
  bool _isAddingComment = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  Future<void> _loadComments() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final comments = await CommentsService.instance.getComments(widget.post.id);
      
      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoading = false;
        });
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error loading comments: $e');
      if (mounted) {
        setState(() {
          _error = 'Failed to load comments';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _addComment(String content) async {
    try {
      setState(() {
        _isAddingComment = true;
      });

      final newComment = await CommentsService.instance.createComment(
        postId: widget.post.id,
        content: content,
      );

      if (newComment != null && mounted) {
        setState(() {
          _comments.add(newComment);
          _isAddingComment = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment added successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error adding comment: $e');
      if (mounted) {
        setState(() {
          _isAddingComment = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add comment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteComment(CommentModel comment) async {
    try {
      final success = await CommentsService.instance.deleteComment(comment.id);
      
      if (success && mounted) {
        setState(() {
          _comments.removeWhere((c) => c.id == comment.id);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deleted successfully!'),
            backgroundColor: AppColors.gfGreen,
          ),
        );
      }
    } catch (e) {
      developer.log('PostDetailScreen: Error deleting comment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete comment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editComment(CommentModel comment) {
    // TODO: Implement comment editing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Comment editing coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Post',
          style: TextStyle(
            color: AppColors.gfOffWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: AppColors.gfOffWhite),
            onPressed: () {
              // TODO: Implement share functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Share feature coming soon!'),
                  backgroundColor: AppColors.gfTeal,
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Post content
          PostCard(
            post: widget.post,
            onTap: () {}, // Disable tap since we're already on the detail screen
            showFullContent: true, // Show full content without truncation
          ),
          
          // Divider
          Container(
            height: 8,
            color: AppColors.gfDarkBackground,
          ),
          
          // Comments section
          Expanded(
            child: _buildCommentsSection(),
          ),
          
          // Add comment widget
          AddCommentWidget(
            postId: widget.post.id,
            onCommentAdded: _addComment,
            isLoading: _isAddingComment,
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsSection() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppColors.gfGrayText,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadComments,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.gfGreen,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_comments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 48,
              color: AppColors.gfGrayText,
            ),
            SizedBox(height: 16),
            Text(
              'No comments yet',
              style: TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Be the first to comment!',
              style: TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadComments,
      color: AppColors.gfGreen,
      backgroundColor: AppColors.gfDarkBackground,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _comments.length,
        itemBuilder: (context, index) {
          final comment = _comments[index];
          return CommentCard(
            comment: comment,
            onEdit: () => _editComment(comment),
            onDelete: () => _deleteComment(comment),
          );
        },
      ),
    );
  }
}
