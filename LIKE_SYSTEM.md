# GameFlex Like System Implementation

This document describes the comprehensive like system implemented for the GameFlex mobile application, including user tracking and real-time features.

## Overview

The like system allows users to:
- Like and unlike posts
- Track their like history
- See real-time updates when posts are liked/unliked
- View which posts they have previously liked
- See accurate like counts and status

## Architecture

### Database Schema

The like system uses the existing database tables:

#### `public.likes` Table
```sql
CREATE TABLE public.likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE,
    comment_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_like_target CHECK (
        (post_id IS NOT NULL AND comment_id IS NULL) OR
        (post_id IS NULL AND comment_id IS NOT NULL)
    ),
    UNIQUE(user_id, post_id),
    UNIQUE(user_id, comment_id)
);
```

#### Database Functions
- `increment_post_likes(post_id UUID)` - Increments like count for a post
- `decrement_post_likes(post_id UUID)` - Decrements like count for a post (minimum 0)

### Flutter Models

#### PostModel
Enhanced with like tracking:
```dart
class PostModel {
  // ... existing fields
  final bool isLikedByCurrentUser; // NEW: Tracks if current user liked this post
}
```

#### LikeModel
New model for like history:
```dart
class LikeModel {
  final String id;
  final String userId;
  final String? postId;
  final String? commentId;
  final DateTime createdAt;
  // User and post information for display
  final String? username;
  final String? displayName;
  final String? avatarUrl;
  final String? postContent;
  final String? postMediaUrl;
}
```

### Services

#### PostsService
Enhanced with like functionality:
- `likePost(String postId)` - Toggle like/unlike for a post
- `hasLikedPost(String postId)` - Check if user has liked a post
- `getUserLikedPosts(String userId)` - Get user's liked posts with pagination
- `getCurrentUserLikedPosts()` - Get current user's liked posts
- `_getLikedPostIds(String userId, List<String> postIds)` - Batch check like status

### Real-time Features

#### Supabase Realtime Integration
The system uses Supabase's real-time features to provide live updates:

1. **Real-time Publication**: Tables are added to `supabase_realtime` publication
2. **WebSocket Subscriptions**: PostsProvider subscribes to changes
3. **Live Updates**: Like counts and status update immediately across all clients

#### PostsProvider Real-time Methods
- `startRealtimeSubscriptions()` - Start listening for changes
- `stopRealtimeSubscriptions()` - Stop listening for changes
- `_handleLikesChange()` - Handle like table changes
- `_handlePostsChange()` - Handle post table changes

## User Interface

### PostCard Widget
- Shows correct like button state (filled/outlined heart)
- Displays current like count
- Provides visual feedback when liked/unliked
- Updates in real-time when other users like/unlike

### LikedPostsScreen
- Displays user's like history
- Shows post content and metadata
- Supports pagination for large like histories
- Pull-to-refresh functionality

### Profile Integration
- "Liked Posts" button in profile screen
- Easy navigation to like history

## Usage Examples

### Liking a Post
```dart
// In PostCard widget
void _handleLike(BuildContext context) {
  final postsProvider = Provider.of<PostsProvider>(context, listen: false);
  postsProvider.toggleLike(post.id);
}
```

### Checking Like Status
```dart
// Posts are loaded with like status included
final posts = await PostsService.instance.getPosts();
for (final post in posts) {
  print('Post ${post.id} is liked: ${post.isLikedByCurrentUser}');
}
```

### Viewing Like History
```dart
// Navigate to liked posts screen
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const LikedPostsScreen(),
  ),
);
```

## Real-time Behavior

### Like Events
When a user likes/unlikes a post:
1. Database is updated immediately
2. Real-time event is broadcast
3. All connected clients receive the update
4. UI updates automatically with new like count
5. Like button state updates for the acting user

### Performance Optimizations
- Batch like status checking for multiple posts
- Efficient database queries with proper indexing
- Real-time subscriptions only for active screens
- Pagination for like history to handle large datasets

## Testing

### Unit Tests
The system includes comprehensive unit tests in `test/like_system_test.dart`:
- PostModel like status handling
- LikeModel creation and parsing
- JSON serialization/deserialization
- Time formatting for like history
- Model equality and hashing

### Running Tests
```bash
flutter test test/like_system_test.dart
```

## Database Setup

### Required Permissions
The like system requires proper Row Level Security (RLS) policies:
```sql
-- Users can view all likes
CREATE POLICY "Likes are viewable by everyone" ON public.likes
    FOR SELECT USING (true);

-- Users can only insert their own likes
CREATE POLICY "Users can insert their own likes" ON public.likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own likes
CREATE POLICY "Users can delete their own likes" ON public.likes
    FOR DELETE USING (auth.uid() = user_id);
```

### Real-time Configuration
Tables must be added to the real-time publication:
```sql
ALTER PUBLICATION supabase_realtime ADD TABLE public.posts;
ALTER PUBLICATION supabase_realtime ADD TABLE public.likes;
```

## Future Enhancements

Potential improvements to the like system:
1. **Like Notifications**: Notify users when their posts are liked
2. **Like Analytics**: Track like patterns and popular content
3. **Bulk Operations**: Like/unlike multiple posts at once
4. **Like Reactions**: Different types of reactions (love, laugh, etc.)
5. **Like Leaderboards**: Show most-liked posts and users
6. **Comment Likes**: Extend system to support comment likes

## Troubleshooting

### Common Issues

1. **Real-time not working**: Check if tables are in publication
2. **Like status incorrect**: Verify RLS policies and user authentication
3. **Performance issues**: Check database indexes and query optimization
4. **UI not updating**: Ensure PostsProvider is properly connected

### Debug Commands
```bash
# Check real-time publication
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';

# Check like counts
SELECT post_id, COUNT(*) as like_count FROM public.likes GROUP BY post_id;

# Check user's likes
SELECT * FROM public.likes WHERE user_id = 'your-user-id';
```

## Conclusion

The GameFlex like system provides a robust, real-time, and user-friendly way for users to interact with posts. The implementation follows best practices for database design, real-time updates, and Flutter state management, ensuring a smooth and responsive user experience.
