#!/usr/bin/env pwsh
# Development Run Script for GameFlex Mobile
# This script runs the app in development mode with local backend URLs

param(
    [string]$Platform = "windows",
    [switch]$Release = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Development Run Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\run-dev.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Release              Run in release mode [default: debug]"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\run-dev.ps1                    # Run Windows debug"
    Write-Host "  .\run-dev.ps1 -Platform android  # Run Android debug"
    Write-Host "  .\run-dev.ps1 -Release           # Run Windows release"
    exit 0
}

Write-Host "🚀 Running GameFlex Mobile in DEVELOPMENT mode" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: $(if ($Release) { 'Release' } else { 'Debug' })" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

# Run based on platform
$runMode = if ($Release) { "--release" } else { "--debug" }

Write-Host "🔧 Development Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses local backend (localhost/********)" -ForegroundColor White
Write-Host "  - Debug logging enabled" -ForegroundColor White
Write-Host "  - Development app name/icon" -ForegroundColor White
Write-Host ""

$modeText = if ($Release) { 'release' } else { 'debug' }

switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏃 Running on Windows ($modeText)..." -ForegroundColor Yellow
        flutter run -d windows $runMode
    }
    "android" {
        Write-Host "🏃 Running on Android ($modeText)..." -ForegroundColor Yellow
        flutter run -d android $runMode --flavor development
    }
    "ios" {
        Write-Host "🏃 Running on iOS ($modeText)..." -ForegroundColor Yellow
        flutter run -d ios $runMode --dart-define=PRODUCTION=false
    }
    "web" {
        Write-Host "🏃 Running on Web ($modeText)..." -ForegroundColor Yellow
        flutter run -d web-server $runMode --web-port 3001
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}
