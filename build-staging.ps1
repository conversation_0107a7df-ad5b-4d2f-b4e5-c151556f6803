#!/usr/bin/env pwsh
# Staging Build Script for GameFlex Mobile
# This script builds the app for staging with dev.api.gameflex.io URLs

param(
    [string]$Platform = "windows",
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Staging Build Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\build-staging.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build-staging.ps1                    # Build Windows staging"
    Write-Host "  .\build-staging.ps1 -Platform android  # Build Android staging"
    Write-Host "  .\build-staging.ps1 -Platform ios      # Build iOS staging"
    Write-Host "  .\build-staging.ps1 -Platform web      # Build Web staging"
    exit 0
}

Write-Host "🚀 Building GameFlex Mobile for STAGING" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: Release (Staging)" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
flutter clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter clean failed" -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

# Build based on platform with staging environment
switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏗️ Building for Windows (staging)..." -ForegroundColor Yellow
        flutter build windows --release --dart-define=STAGING=true
    }
    "android" {
        Write-Host "🏗️ Building for Android (staging)..." -ForegroundColor Yellow
        flutter build apk --release --flavor staging --dart-define=STAGING=true
    }
    "ios" {
        Write-Host "🏗️ Building for iOS (staging)..." -ForegroundColor Yellow
        flutter build ios --release --dart-define=STAGING=true
    }
    "web" {
        Write-Host "🏗️ Building for Web (staging)..." -ForegroundColor Yellow
        flutter build web --release --dart-define=STAGING=true
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Staging build completed successfully!" -ForegroundColor Green

# Show build output location
switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "📁 Build output: build\windows\x64\runner\release\" -ForegroundColor Cyan
    }
    "android" {
        Write-Host "📁 Build output: build\app\outputs\flutter-apk\app-staging-release.apk" -ForegroundColor Cyan
    }
    "ios" {
        Write-Host "📁 Build output: build\ios\iphoneos\Runner.app" -ForegroundColor Cyan
    }
    "web" {
        Write-Host "📁 Build output: build\web\" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🔧 Production Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses production backend (https://api.gameflex.io)" -ForegroundColor White
Write-Host "  - Optimized release build" -ForegroundColor White
Write-Host "  - Production app name/icon" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Important Notes:" -ForegroundColor Red
Write-Host "  - Ensure production backend is accessible" -ForegroundColor White
Write-Host "  - Test thoroughly before deployment" -ForegroundColor White
Write-Host "  - Consider code signing for distribution" -ForegroundColor White
